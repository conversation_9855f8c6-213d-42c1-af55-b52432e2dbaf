<style>
    .lokasi-radiasi-group {
        border-left: 4px solid #007bff;
        padding-left: 15px;
        border-radius: 5px;
        margin-bottom: 15px;
    }

    .simulator-reference-group {
        border-left: 4px solid #28a745;
        padding-left: 15px;
        border-radius: 5px;
        margin-bottom: 15px;
    }

    .lokasi-radiasi-group label {
        color: #007bff;
        font-weight: bold;
    }

    .simulator-reference-group label {
        color: #28a745;
        font-weight: bold;
    }

    .info-item {
        margin-bottom: 15px;
    }

    .info-label {
        font-weight: bold;
        color: #e9ecef;
        display: block;
        margin-bottom: 5px;
        font-size: 0.9em;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    }

    .info-value {
        display: block;
        padding: 10px 14px;
        background-color: #495057;
        border: 1px solid #6c757d;
        border-radius: 6px;
        font-size: 0.95em;
        color: #f8f9fa;
        min-height: 40px;
        line-height: 1.4;
        box-shadow: inset 0 1px 3px rgba(0,0,0,0.2);
        transition: all 0.2s ease;
    }

    .info-value:hover {
        background-color: #5a6268;
        border-color: #7a8288;
    }

    /* Dark theme card styling */
    .modal-detail-card {
        background-color: #343a40;
        border: 1px solid #495057;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    .modal-detail-card .card-header {
        background-color: #495057;
        border-bottom: 1px solid #6c757d;
        color: #f8f9fa;
    }

    .modal-detail-card .card-body {
        background-color: #3a4046;
    }

    /* Modal content dark theme */
    #modal-detail .modal-content {
        background-color: #2c3034;
        border: 1px solid #495057;
    }

    #modal-detail .modal-header {
        border-bottom: 1px solid #495057;
    }

    #modal-detail .modal-body {
        background-color: #343a40;
    }

    /* Dark theme table styling */
    #modal-detail .table-bordered {
        border-color: #495057;
    }

    #modal-detail .table-bordered th,
    #modal-detail .table-bordered td {
        border-color: #495057;
    }

    #modal-detail .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(255,255,255,0.05);
    }

    #modal-detail .table {
        color: #f8f9fa;
        background-color: transparent;
    }

    /* Form controls in dark theme */
    #modal-detail .form-control {
        background-color: #495057;
        border-color: #6c757d;
        color: #f8f9fa;
    }

    #modal-detail .form-control:focus {
        background-color: #5a6268;
        border-color: #80bdff;
        color: #f8f9fa;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    }

    #modal-detail .form-control::placeholder {
        color: #adb5bd;
    }

    /* Button styling in modal */
    #modal-detail .btn-success {
        background-color: #28a745;
        border-color: #28a745;
    }

    #modal-detail .btn-success:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }

    /* Input group styling untuk field dengan satuan */
    #modal-detail .input-group-text {
        background-color: #6c757d;
        border-color: #6c757d;
        color: #f8f9fa;
        font-weight: bold;
        min-width: 50px;
        justify-content: center;
    }

    #modal-edit-detail .input-group-text {
        background-color: #6c757d;
        border-color: #6c757d;
        color: #f8f9fa;
        font-weight: bold;
        min-width: 50px;
        justify-content: center;
    }

    /* Pastikan input number tidak memiliki spinner yang mengganggu */
    input[type="number"]::-webkit-outer-spin-button,
    input[type="number"]::-webkit-inner-spin-button {
        -webkit-appearance: none;
        appearance: none;
        margin: 0;
    }

    input[type="number"] {
        -moz-appearance: textfield;
        appearance: textfield;
    }
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4>Laporan Tindakan Radiasi Eksterna</h4>
                </div>
                <div class="card-body">
                    <?php if ($this->session->userdata('profesi') == 8) : ?>
                        <form id="form-radiasi-eksterna">
                            <input type="hidden" name="nokun" value="<?= $nokun ?>">
                            <input type="hidden" name="id_ctsim" id="id_ctsim">
                            <input type="hidden" name="category" id="category">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group simulator-reference-group">
                                        <label for="simulator_dropdown">Diagnosa</label>
                                        <select class="form-control select2-simulator" id="simulator_dropdown" name="simulator_dropdown" style="width: 100%;">
                                            <option value="">-- Pilih diagnosa --</option>
                                            <?php foreach ($simulator_data as $sim) : ?>
                                                <option value="<?= htmlspecialchars($sim['text']) ?>" data-id="<?= $sim['id'] ?>" data-category="<?= $sim['category'] ?>"><?= htmlspecialchars($sim['text']) ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                        <small class="form-text text-muted">Pilih diagnosa yang sesuai</small>
                                    </div>
                                    <div class="form-group lokasi-radiasi-group">
                                        <label for="lokasi">Lokasi Radiasi<span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="lokasi" name="lokasi" required placeholder="Masukkan lokasi radiasi">
                                        <small class="form-text text-muted">Masukkan lokasi radiasi secara manual</small>
                                    </div>
                                    <div class="form-group">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fa fa-save"></i> Simpan
                                        </button>
                                        <button type="reset" class="btn btn-secondary">
                                            <i class="fa fa-refresh"></i> Reset
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    <?php else : ?>
                        <div class="alert alert-warning">
                            <i class="fa fa-exclamation-triangle"></i>
                            Hanya radiografer yang dapat melakukan input data radiasi eksterna.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4>Data Radiasi Eksterna</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="tabel-radiasi-eksterna" class="table table-bordered table-striped">
                            <thead class="thead-dark">
                                <tr>
                                    <th>No</th>
                                    <th>Lokasi</th>
                                    <th>Diagnosa</th>
                                    <th>Oleh</th>
                                    <th>Waktu</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Edit Lokasi -->
<div class="modal fade" id="modal-edit-lokasi" tabindex="-1" role="dialog" aria-labelledby="modal-edit-lokasi-label" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="modal-edit-lokasi-label"><i class="fa fa-edit"></i> Edit Lokasi Radiasi & diagnosa</h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="form-edit-lokasi">
                    <input type="hidden" name="id" id="edit_id_radiasi">
                    <input type="hidden" name="id_ctsim" id="edit_id_ctsim">
                    <input type="hidden" name="category" id="edit_category">

                    <div class="form-group simulator-reference-group">
                        <label for="edit_simulator_dropdown">Diagnosa</label>
                        <select class="form-control" id="edit_simulator_dropdown" name="simulator_dropdown" style="width: 100%;">
                            <option value="">-- Pilih diagnosa --</option>
                            <?php foreach ($simulator_data as $sim) : ?>
                                <option value="<?= htmlspecialchars($sim['text']) ?>" data-id="<?= $sim['id'] ?>" data-category="<?= $sim['category'] ?>"><?= htmlspecialchars($sim['text']) ?></option>
                            <?php endforeach; ?>
                        </select>
                        <small class="form-text text-muted">Pilih diagnosa yang sesuai</small>
                    </div>

                    <div class="form-group lokasi-radiasi-group">
                        <label for="edit_lokasi">Lokasi Radiasi <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_lokasi" name="lokasi" required>
                        <small class="form-text text-muted">Masukkan lokasi radiasi secara manual</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal"><i class="fa fa-times"></i> Tutup</button>
                <button type="button" class="btn btn-primary" id="btn-update-lokasi"><i class="fa fa-save"></i> Simpan Perubahan</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Edit Detail -->
<div class="modal fade" id="modal-edit-detail" tabindex="-1" role="dialog" aria-labelledby="modal-edit-detail-label" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="modal-edit-detail-label"><i class="fa fa-edit"></i> Edit Detail Radiasi</h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="form-edit-detail">
                    <input type="hidden" name="id_detail" id="edit_id_detail">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_tanggal">Tanggal <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="edit_tanggal" name="tanggal" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_energi">Energi <span class="text-danger">*</span></label>
                                <select class="form-control select2-energi-edit" id="edit_energi" name="energi" required style="width: 100%;">
                                    <option value="">-- Pilih Energi --</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_ssd_sad">SSD/SAD <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="text" class="form-control decimal-input" id="edit_ssd_sad" name="ssd_sad" required placeholder="100 atau 100,5" pattern="^\d+([,.]\d+)?$" title="Masukkan angka dengan format: 100 atau 100,5">
                                    <div class="input-group-append">
                                        <span class="input-group-text">cm</span>
                                    </div>
                                </div>
                                <small class="form-text text-muted">Gunakan koma (,) atau titik (.) untuk desimal</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_dosis_fraksi">Dosis/Fraksi <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="text" class="form-control decimal-input" id="edit_dosis_fraksi" name="dosis_fraksi" required placeholder="2 atau 2,5" pattern="^\d+([,.]\d+)?$" title="Masukkan angka dengan format: 2 atau 2,5">
                                    <div class="input-group-append">
                                        <span class="input-group-text">Gy</span>
                                    </div>
                                </div>
                                <small class="form-text text-muted">Gunakan koma (,) atau titik (.) untuk desimal</small>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal"><i class="fa fa-times"></i> Tutup</button>
                <button type="button" class="btn btn-success" id="btn-update-detail"><i class="fa fa-save"></i> Simpan Perubahan</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Detail -->
<div class="modal fade" id="modal-detail" tabindex="-1" role="dialog" aria-labelledby="modal-detail-label" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header text-white">
                <h5 class="modal-title" id="modal-detail-label">
                    <i class="fa fa-list"></i> Detail Radiasi Eksterna
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Info Radiasi Utama -->
                <div class="card mb-3 modal-detail-card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fa fa-info-circle text-info"></i> Informasi Radiasi Eksterna</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="info-item">
                                    <label class="info-label">Nama Pasien:</label>
                                    <span class="info-value" id="info-nama">-</span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="info-item">
                                    <label class="info-label">No. MR:</label>
                                    <span class="info-value" id="info-nomr">-</span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="info-item">
                                    <label class="info-label">Tanggal Lahir:</label>
                                    <span class="info-value" id="info-tgl-lahir">-</span>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="info-item">
                                    <label class="info-label">Lokasi Radiasi:</label>
                                    <span class="info-value" id="info-lokasi">-</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-item">
                                    <label class="info-label">diagnosa:</label>
                                    <span class="info-value" id="info-catatan-simulator">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if ($this->session->userdata('profesi') == 8) : ?>
                    <!-- Form Input Detail -->
                    <div class="card mb-3 modal-detail-card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fa fa-plus text-success"></i> Tambah Detail Radiasi</h6>
                        </div>
                        <div class="card-body">
                            <form id="form-radiasi-eksterna-detail">
                                <input type="hidden" name="id_radiasi" id="id_radiasi">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="tanggal">Tanggal <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control" id="tanggal" name="tanggal" required>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="energi">Energi <span class="text-danger">*</span></label>
                                            <select class="form-control select2-energi" id="energi" name="energi" required style="width: 100%;">
                                                <option value="">-- Pilih Energi --</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="ssd_sad">SSD/SAD <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="text" class="form-control decimal-input" id="ssd_sad" name="ssd_sad" required placeholder="100 atau 100,5" pattern="^\d+([,.]\d+)?$" title="Masukkan angka dengan format: 100 atau 100,5">
                                                <div class="input-group-append">
                                                    <span class="input-group-text">cm</span>
                                                </div>
                                            </div>
                                            <small class="form-text text-muted">Gunakan koma (,) atau titik (.) untuk desimal</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="dosis_fraksi">Dosis/Fraksi <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="text" class="form-control decimal-input" id="dosis_fraksi" name="dosis_fraksi" required placeholder="2 atau 2,5" pattern="^\d+([,.]\d+)?$" title="Masukkan angka dengan format: 2 atau 2,5">
                                                <div class="input-group-append">
                                                    <span class="input-group-text">Gy</span>
                                                </div>
                                            </div>
                                            <small class="form-text text-muted">Gunakan koma (,) atau titik (.) untuk desimal</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fa fa-save"></i> Simpan Detail
                                    </button>
                                    <button type="reset" class="btn btn-secondary">
                                        <i class="fa fa-refresh"></i> Reset
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php else : ?>
                    <div class="alert alert-warning">
                        <i class="fa fa-exclamation-triangle"></i>
                        Hanya radiografer yang dapat menambah detail radiasi.
                    </div>
                <?php endif; ?>

                <!-- Tabel Detail -->
                <div class="card modal-detail-card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fa fa-table text-warning"></i> Data Detail Radiasi</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="tabel-radiasi-eksterna-detail" class="table table-bordered table-striped" style="width:100%">
                                <thead class="thead-dark">
                                    <tr>
                                        <th>No</th>
                                        <th>Tanggal</th>
                                        <th>Waktu</th>
                                        <th>Energi</th>
                                        <th>SSD/SAD</th>
                                        <th>Dosis/Fraksi</th>
                                        <th>RTT</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fa fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Fungsi untuk normalisasi input decimal untuk tipe FLOAT
    function normalizeDecimalInput(value) {
        if (!value) return '';
        // Untuk tipe FLOAT database, kita tetap normalisasi ke titik
        // tapi database FLOAT sudah support koma juga
        return value.toString().replace(',', '.');
    }

    // Fungsi untuk validasi input decimal
    function validateDecimalInput(value) {
        if (!value) return false;
        // Regex untuk validasi angka decimal (dengan koma atau titik)
        var decimalRegex = /^\d+([,.]\d+)?$/;
        return decimalRegex.test(value.toString());
    }

    // Fungsi untuk format display decimal (gunakan koma untuk tampilan Indonesia)
    function formatDecimalDisplay(value) {
        if (!value) return '';
        return value.toString().replace('.', ',');
    }

    // Event handler untuk input decimal fields
    function setupDecimalInput(selector) {
        $(document).on('input', selector, function() {
            var $this = $(this);
            var value = $this.val();

            // Hanya izinkan angka, koma, dan titik
            value = value.replace(/[^0-9,.]/g, '');

            // Pastikan hanya ada satu pemisah desimal
            var parts = value.split(/[,.]/);
            if (parts.length > 2) {
                // Jika ada lebih dari satu pemisah, ambil yang pertama
                value = parts[0] + ',' + parts[1];
            }

            $this.val(value);
        });

        // Validasi saat blur (kehilangan fokus)
        $(document).on('blur', selector, function() {
            var $this = $(this);
            var value = $this.val();

            if (value && !validateDecimalInput(value)) {
                alertify.error('Format angka tidak valid. Gunakan format: 123 atau 123,45');
                $this.focus();
                return false;
            }
        });
    }

    // Setup decimal input untuk semua field SSD/SAD dan Dosis/Fraksi
    setupDecimalInput('#ssd_sad, #edit_ssd_sad, #dosis_fraksi, #edit_dosis_fraksi');
    // Inisialisasi select2 untuk dropdown simulator utama
    $('.select2-simulator').select2({
        placeholder: "Pilih diagnosa",
        allowClear: true
    });

    // Inisialisasi select2 untuk dropdown simulator di modal edit
    $('#edit_simulator_dropdown').select2({
        placeholder: "Pilih diagnosa",
        allowClear: true,
        dropdownParent: $('#modal-edit-lokasi')
    });

    // Inisialisasi select2 untuk dropdown energi di modal detail
    $('.select2-energi').select2({
        placeholder: "-- Pilih Energi --",
        allowClear: true,
        dropdownParent: $('#modal-detail'),
        ajax: {
            url: '<?= base_url('radioterapi/LaporanRadiasiEksterna/get_energi_options') ?>',
            dataType: 'json',
            delay: 250,
            data: function(params) {
                return {
                    q: params.term
                };
            },
            processResults: function(data) {
                return data;
            },
            cache: true
        }
    });

    // Inisialisasi select2 untuk dropdown energi di modal edit detail
    $('.select2-energi-edit').select2({
        placeholder: "-- Pilih Energi --",
        allowClear: true,
        dropdownParent: $('#modal-edit-detail'),
        ajax: {
            url: '<?= base_url('radioterapi/LaporanRadiasiEksterna/get_energi_options') ?>',
            dataType: 'json',
            delay: 250,
            data: function(params) {
                return {
                    q: params.term
                };
            },
            processResults: function(data) {
                return data;
            },
            cache: true
        }
    });

    // Dropdown simulator wajib diisi dan terpisah dari field lokasi
    $('#simulator_dropdown').on('change', function() {
        var selected_option = $(this).find('option:selected');
        var selected_text = selected_option.val();
        var id_ctsim = selected_option.data('id');
        var category = selected_option.data('category');

        // Simpan data referensi simulator
        if (selected_text) {
            $('#id_ctsim').val(id_ctsim);
            $('#category').val(category);
        } else {
            $('#id_ctsim').val('');
            $('#category').val('');
        }
    });

    // Handler untuk dropdown simulator di modal edit
    $('#edit_simulator_dropdown').on('change', function() {
        var selected_option = $(this).find('option:selected');
        var selected_text = selected_option.val();
        var id_ctsim = selected_option.data('id');
        var category = selected_option.data('category');

        // Simpan data referensi simulator
        if (selected_text) {
            $('#edit_id_ctsim').val(id_ctsim);
            $('#edit_category').val(category);
        } else {
            $('#edit_id_ctsim').val('');
            $('#edit_category').val('');
        }
    });

    // Handler untuk tombol reset form utama
    $('#form-radiasi-eksterna button[type="reset"]').on('click', function() {
        // Reset dropdown simulator juga
        $('.select2-simulator').val('').trigger('change');
    });

    // --- FIX MODAL STACKING ---
    // Fungsi ini akan mengatur z-index secara dinamis setiap kali modal baru ditampilkan
    $(document).on('show.bs.modal', '.modal', function() {
        var zIndex = 1040 + (10 * $('.modal:visible').length);
        $(this).css('z-index', zIndex);
        // Atur z-index untuk backdrop-nya agar berada di belakang modal yang baru
        setTimeout(function() {
            $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
        }, 0);
    });

    // Atur ulang body saat semua modal ditutup
    $(document).on('hidden.bs.modal', '.modal', function () {
        $('.modal:visible').length && $(document.body).addClass('modal-open');
    });
    // --- END FIX ---

    var nokun = '<?= $nokun ?>';
    var nomr = '<?= $nomr ?>';
    var table, tableDetail;

    // Initialize DataTable untuk tabel utama
    table = $('#tabel-radiasi-eksterna').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "<?= base_url('radioterapi/LaporanRadiasiEksterna/get_data') ?>",
            "type": "GET",
            "data": function(d) {
                d.nomr = nomr;
            }
        },
        "columns": [
            {"data": 0, "orderable": false}, // No
            {"data": 1}, // Lokasi
            {"data": 2}, // diagnosa
            {"data": 3}, // Oleh
            {"data": 4}, // Waktu
            {"data": 5, "orderable": false} // Aksi
        ],
        "responsive": true,
        "pageLength": 10,
        // Nonaktifkan initial ordering dari frontend agar backend default ($this->order) dipakai
        "order": []
    });

    // Form submit untuk radiasi eksterna utama
    $('#form-radiasi-eksterna').on('submit', function(e) {
        e.preventDefault();

        $.ajax({
            url: '<?= base_url('radioterapi/LaporanRadiasiEksterna/save') ?>',
            type: 'POST',
            data: $(this).serialize(),
            dataType: 'json',
            beforeSend: function() {
                $('button[type="submit"]').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Menyimpan...');
            },
            success: function(response) {
                if (response.status) {
                    alertify.success(response.message || 'Data berhasil disimpan');
                    $('#form-radiasi-eksterna')[0].reset();
                    // Reset dropdown simulator juga
                    $('.select2-simulator').val('').trigger('change');
                    table.ajax.reload(null, false);
                } else {
                    alertify.error(response.message || 'Terjadi kesalahan');
                }
            },
            error: function() {
                alertify.error('Terjadi kesalahan sistem');
            },
            complete: function() {
                $('button[type="submit"]').prop('disabled', false).html('<i class="fa fa-save"></i> Simpan');
            }
        });
    });

    // Handle click tombol detail untuk membuka modal detail
    $(document).on('click', '.detail-btn', function() {
        var id = $(this).data('id');

        // Get data radiasi utama
        $.ajax({
            url: '<?= base_url('radioterapi/LaporanRadiasiEksterna/get_radiasi_by_id') ?>',
            type: 'GET',
            data: {id: id},
            dataType: 'json',
            success: function(response) {
                if (response) {
                    $('#id_radiasi').val(response.id);
                    $('#info-lokasi').text(response.lokasi || '-');
                    $('#info-nama').text(response.nama_pasien || '-');
                    $('#info-nomr').text(response.nomr || '-');
                    $('#info-catatan-simulator').text(response.catatan_simulator || 'Tidak ada catatan');

                    // Format tanggal lahir
                    if (response.tanggal_lahir) {
                        var tgl_lahir = new Date(response.tanggal_lahir).toLocaleDateString('id-ID', {
                            day: '2-digit',
                            month: '2-digit',
                            year: 'numeric'
                        });
                        $('#info-tgl-lahir').text(tgl_lahir);
                    } else {
                        $('#info-tgl-lahir').text('-');
                    }


                    // Initialize DataTable untuk detail
                    tableDetail = $('#tabel-radiasi-eksterna-detail').DataTable({
                        "destroy": true,
                        "processing": true,
                        "serverSide": true,
                        "ajax": {
                            "url": "<?= base_url('radioterapi/LaporanRadiasiEksterna/get_detail_data') ?>",
                            "type": "GET",
                            "data": function(d) {
                                d.id_radiasi = id;
                            }
                        },
                        "columns": [
                            {"data": 0, "orderable": false}, // No
                            {"data": 1}, // Tanggal
                            {"data": 2}, // Waktu
                            {"data": 3}, // Energi
                            {"data": 4}, // SSD/SAD
                            {"data": 5}, // Dosis/Fraksi
                            {"data": 6}, // RTT
                            {"data": 7, "orderable": false} // Aksi
                        ],
                        "responsive": true,
                        "pageLength": 5,
                        // Gunakan default order dari backend (tanggal desc, id desc)
                        "order": []
                    });

                    // Set tanggal hari ini sebagai default setiap kali modal dibuka
                    $('#tanggal').val(new Date().toISOString().split('T')[0]);
                    $('#modal-detail').modal('show');
                }
            }
        });
    });

    // Handle click tombol edit lokasi untuk membuka modal edit
    $(document).on('click', '.edit-lokasi-btn', function() {
        var id = $(this).data('id');
        $.ajax({
            url: '<?= base_url('radioterapi/LaporanRadiasiEksterna/get_radiasi_by_id') ?>',
            type: 'GET',
            data: { id: id },
            dataType: 'json',
            success: function(response) {
                if (response) {
                    $('#edit_id_radiasi').val(response.id);
                    $('#edit_lokasi').val(response.lokasi);
                    $('#edit_id_ctsim').val(response.id_ctsim);
                    $('#edit_category').val(response.category);

                    // Set nilai dropdown simulator berdasarkan data yang ada
                    if (response.id_ctsim && response.category) {
                        // Cari option yang sesuai dengan id_ctsim dan category
                        var targetOption = $('#edit_simulator_dropdown option').filter(function() {
                            return $(this).data('id') == response.id_ctsim && $(this).data('category') == response.category;
                        });

                        if (targetOption.length > 0) {
                            $('#edit_simulator_dropdown').val(targetOption.val()).trigger('change');
                        }
                    } else {
                        $('#edit_simulator_dropdown').val('').trigger('change');
                    }

                    $('#modal-edit-lokasi').modal('show');
                } else {
                    alertify.error('Data tidak ditemukan.');
                }
            },
            error: function() {
                alertify.error('Gagal mengambil data.');
            }
        });
    });

    // Handle submit form edit lokasi
    $('#btn-update-lokasi').on('click', function() {
        $.ajax({
            url: '<?= base_url('radioterapi/LaporanRadiasiEksterna/update') ?>',
            type: 'POST',
            data: $('#form-edit-lokasi').serialize(),
            dataType: 'json',
            beforeSend: function() {
                $(this).prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Menyimpan...');
            },
            success: function(response) {
                if (response.status) {
                    alertify.success(response.message);
                    $('#modal-edit-lokasi').modal('hide');
                    table.ajax.reload(null, false);
                } else {
                    alertify.error(response.message);
                }
            },
            error: function() {
                alertify.error('Terjadi kesalahan sistem.');
            },
            complete: function() {
                $('#btn-update-lokasi').prop('disabled', false).html('<i class="fa fa-save"></i> Simpan Perubahan');
            }
        });
    });

    // Handle click tombol edit detail
    $(document).on('click', '.edit-detail-btn', function() {
        var id = $(this).data('id');
        $.ajax({
            url: '<?= base_url('radioterapi/LaporanRadiasiEksterna/get_detail_by_id') ?>',
            type: 'GET',
            data: { id: id },
            dataType: 'json',
            success: function(response) {
                if (response) {
                    $('#edit_id_detail').val(response.id);
                    $('#edit_tanggal').val(response.tanggal);

                    // Set nilai energi untuk Select2
                    if (response.energi) {
                        // Jika energi adalah ID variabel, ambil text dari server
                        $.ajax({
                            url: '<?= base_url('radioterapi/LaporanRadiasiEksterna/get_energi_options') ?>',
                            type: 'GET',
                            data: { id: response.energi },
                            dataType: 'json',
                            success: function(energiData) {
                                if (energiData.results && energiData.results.length > 0) {
                                    // Buat option baru dan set sebagai selected
                                    var option = new Option(energiData.results[0].text, energiData.results[0].id, true, true);
                                    $('#edit_energi').append(option).trigger('change');
                                } else {
                                    // Fallback: set sebagai text biasa jika tidak ditemukan di master
                                    var option = new Option(response.energi, response.energi, true, true);
                                    $('#edit_energi').append(option).trigger('change');
                                }
                            },
                            error: function() {
                                // Fallback: set sebagai text biasa
                                var option = new Option(response.energi, response.energi, true, true);
                                $('#edit_energi').append(option).trigger('change');
                            }
                        });
                    } else {
                        $('#edit_energi').val('').trigger('change');
                    }

                    // Format display dengan koma untuk tampilan Indonesia
                    $('#edit_ssd_sad').val(formatDecimalDisplay(response.ssd_sad));
                    $('#edit_dosis_fraksi').val(formatDecimalDisplay(response.dosis_fraksi));
                    $('#modal-edit-detail').modal('show');
                } else {
                    alertify.error('Data detail tidak ditemukan.');
                }
            },
            error: function() {
                alertify.error('Gagal mengambil data detail.');
            }
        });
    });

    // Handle submit form edit detail
    $('#btn-update-detail').on('click', function() {
        // Normalisasi data decimal sebelum submit
        // Database FLOAT sudah support koma, tapi kita tetap normalisasi untuk konsistensi
        var formData = $('#form-edit-detail').serializeArray();
        var normalizedData = {};

        $.each(formData, function(i, field) {
            if (field.name === 'ssd_sad' || field.name === 'dosis_fraksi') {
                // Normalisasi input decimal (koma ke titik) untuk konsistensi
                normalizedData[field.name] = normalizeDecimalInput(field.value);
            } else {
                normalizedData[field.name] = field.value;
            }
        });

        $.ajax({
            url: '<?= base_url('radioterapi/LaporanRadiasiEksterna/update_detail') ?>',
            type: 'POST',
            data: normalizedData,
            dataType: 'json',
            beforeSend: function() {
                $(this).prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Menyimpan...');
            },
            success: function(response) {
                if (response.status) {
                    alertify.success(response.message);
                    $('#modal-edit-detail').modal('hide');
                    if (tableDetail) {
                        tableDetail.ajax.reload();
                    }
                } else {
                    alertify.error(response.message);
                }
            },
            error: function() {
                alertify.error('Terjadi kesalahan sistem.');
            },
            complete: function() {
                $('#btn-update-detail').prop('disabled', false).html('<i class="fa fa-save"></i> Simpan Perubahan');
            }
        });
    });

    // Form submit untuk detail radiasi eksterna
    $('#form-radiasi-eksterna-detail').on('submit', function(e) {
        e.preventDefault();

        // Normalisasi data decimal sebelum submit
        // Database FLOAT sudah support koma, tapi kita tetap normalisasi untuk konsistensi
        var formData = $(this).serializeArray();
        var normalizedData = {};

        $.each(formData, function(i, field) {
            if (field.name === 'ssd_sad' || field.name === 'dosis_fraksi') {
                // Normalisasi input decimal (koma ke titik) untuk konsistensi
                normalizedData[field.name] = normalizeDecimalInput(field.value);
            } else {
                normalizedData[field.name] = field.value;
            }
        });

        $.ajax({
            url: '<?= base_url('radioterapi/LaporanRadiasiEksterna/save_detail') ?>',
            type: 'POST',
            data: normalizedData,
            dataType: 'json',
            beforeSend: function() {
                $('#form-radiasi-eksterna-detail button[type="submit"]').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Menyimpan...');
            },
            success: function(response) {
                if (response.status) {
                    alertify.success(response.message || 'Detail berhasil disimpan');
                    $('#form-radiasi-eksterna-detail')[0].reset();
                    // Reset Select2 energi setelah submit berhasil
                    $('.select2-energi').val('').trigger('change');
                    if (tableDetail) {
                        tableDetail.ajax.reload();
                    }
                } else {
                    alertify.error(response.message || 'Terjadi kesalahan');
                }
            },
            error: function() {
                alertify.error('Terjadi kesalahan sistem');
            },
            complete: function() {
                $('#form-radiasi-eksterna-detail button[type="submit"]').prop('disabled', false).html('<i class="fa fa-save"></i> Simpan Detail');
            }
        });
    });

    // Handler untuk tombol reset form detail
    $('#form-radiasi-eksterna-detail button[type="reset"]').on('click', function() {
        // Reset Select2 energi juga
        $('.select2-energi').val('').trigger('change');
    });

    // Reset form dan hancurkan datatable ketika modal ditutup
    $('#modal-detail').on('hidden.bs.modal', function() {
        $(this).find('form')[0].reset();
        // Reset Select2 energi
        $('.select2-energi').val('').trigger('change');
        if ($.fn.DataTable.isDataTable('#tabel-radiasi-eksterna-detail')) {
            $('#tabel-radiasi-eksterna-detail').DataTable().destroy();
        }
        $('#tabel-radiasi-eksterna-detail tbody').empty();
        tableDetail = null;
    });

    // Reset form edit detail ketika modal ditutup
    $('#modal-edit-detail').on('hidden.bs.modal', function() {
        $(this).find('form')[0].reset();
        // Reset Select2 energi edit
        $('.select2-energi-edit').val('').trigger('change');
    });

    // Adjust table layout after modal is shown
    $('#modal-detail').on('shown.bs.modal', function () {
        if (tableDetail) {
            tableDetail.columns.adjust().responsive.recalc();
        }
    });

    // Handle click tombol hapus utama
    $(document).on('click', '.delete-btn', function() {
        var id = $(this).data('id');
        alertify.confirm('Konfirmasi', 'Anda yakin ingin menghapus data ini?', function() {
            $.ajax({
                url: '<?= base_url('radioterapi/LaporanRadiasiEksterna/delete/') ?>' + id,
                type: 'POST',
                dataType: 'json',
                success: function(response) {
                    if (response.status) {
                        alertify.success(response.message);
                        table.ajax.reload(null, false);
                    } else {
                        alertify.error(response.message);
                    }
                },
                error: function() {
                    alertify.error('Terjadi kesalahan sistem saat menghapus.');
                }
            });
        }, function() {
            alertify.error('Hapus data dibatalkan');
        });
    });

    // Handle click tombol hapus detail
    $(document).on('click', '.delete-detail-btn', function() {
        var id = $(this).data('id');
        alertify.confirm('Konfirmasi', 'Anda yakin ingin menghapus detail ini?', function() {
            $.ajax({
                url: '<?= base_url('radioterapi/LaporanRadiasiEksterna/delete_detail/') ?>' + id,
                type: 'POST',
                dataType: 'json',
                success: function(response) {
                    if (response.status) {
                        alertify.success(response.message);
                        if (tableDetail) {
                            tableDetail.ajax.reload();
                        }
                    } else {
                        alertify.error(response.message);
                    }
                },
                error: function() {
                    alertify.error('Terjadi kesalahan sistem saat menghapus detail.');
                }
            });
        }, function() {
            alertify.error('Hapus data dibatalkan');
        });
    });
});
</script>
